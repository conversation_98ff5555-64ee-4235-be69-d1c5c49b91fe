package me.darkness.featheraddons;

import me.darkness.featheraddons.commands.FeatherAddonsCommand;
import me.darkness.featheraddons.configuration.Configuration;
import me.darkness.featheraddons.configuration.Messages;
import me.darkness.featheraddons.listeners.FeatherPlayerListener;
import me.darkness.featheraddons.managers.DiscordManager;
import me.darkness.featheraddons.managers.ModManager;
import me.darkness.featheraddons.managers.ServerBackgroundManager;
import org.bukkit.Bukkit;
import org.bukkit.plugin.java.JavaPlugin;

import java.util.logging.Level;

public final class Main extends JavaPlugin {

    private static Main instance;

    private Configuration configuration;
    private Messages messages;
    private ModManager modManager;
    private DiscordManager discordManager;
    private ServerBackgroundManager backgroundManager;

    @Override
    public void onEnable() {
        instance = this;

        if (!Bukkit.getPluginManager().isPluginEnabled("FeatherServerAPI")) {
            getLogger().severe("Nie znaleziono pluginu FeatherServerAPI. Jest on potrzebny do dzialania pluginu");
            getLogger().severe("Link do pobrania: https://github.com/FeatherMC/feather-server-api/releases");
            Bukkit.getPluginManager().disablePlugin(this);
            return;
        }

        createDirectories();
        loadConfiguration();
        getServer().getConsoleSender().sendMessage("§8[§a§l777-Plots§8] §f§nZaladowano konfiguracje§r.");
        initializeManagers();
        getServer().getConsoleSender().sendMessage("§8[§a§l777-Plots§8] §f§nPolaczono z FeatherApi§r.");
        setupCommands();
        getServer().getConsoleSender().sendMessage("§8[§a§l777-Plots§8] §f§nZarejestrowano komendy§r.");
        getServer().getConsoleSender().sendMessage("§8[§6§l777-FeatherAddons§8] §aUruchomiono plugin!");
    }

    @Override
    public void onDisable() {
        getServer().getConsoleSender().sendMessage("§8[§4§l777-FeatherAddons§8] §cWyłączono plugin :C");

        if (discordManager != null) {
            try {
                discordManager.shutdown();
            } catch (Exception e) {
            }
        }

        getServer().getConsoleSender().sendMessage("§8[§4§l777-FeatherAddons§8] §cWyłączono plugin :C");
    }

    private void createDirectories() {
        if (!getDataFolder().exists()) {
            getDataFolder().mkdirs();
        }
    }

    private void loadConfiguration() {
        saveDefaultConfig();
        configuration = new Configuration(this);
        configuration.load();
        messages = new Messages(this);
    }

    private void initializeManagers() {
        try {
            modManager = new ModManager(this);
        } catch (Exception e) {
            getLogger().severe("Blad przy zarzadzaniem modami: " + e.getMessage());
        }

        try {
            discordManager = new DiscordManager(this);
        } catch (Exception e) {
            getLogger().warning("Blad z Discord Rich Presence: " + e.getMessage());
            getLogger().warning("Wylaczam opcje Discord Rich Presence..");
        }

        try {
            backgroundManager = new ServerBackgroundManager(this);
        } catch (Exception e) {
            getLogger().warning("Blad przy wlaczeniu tla serwera: " + e.getMessage());
            getLogger().warning("Wylaczam tlo serwera..");
        }
    }

    private void setupCommands() {
        FeatherAddonsCommand mainCommand = new FeatherAddonsCommand(this);
        getCommand("777featheraddons").setExecutor(mainCommand);
        getCommand("777featheraddons").setTabCompleter(mainCommand);

        Bukkit.getPluginManager().registerEvents(new FeatherPlayerListener(this), this);
    }

    public void reloadConfiguration() {
        reloadConfig();
        configuration.load();

        if (modManager != null) {
            modManager.reload();
        }
        if (discordManager != null) {
            discordManager.reload();
        }
        if (backgroundManager != null) {
            backgroundManager.reload();
        }
    }

    public Configuration getConfiguration() {
        return configuration;
    }

    public ModManager getModManager() {
        return modManager;
    }

    public DiscordManager getDiscordManager() {
        return discordManager;
    }

    public Messages getMessages() {
        return messages;
    }
}
