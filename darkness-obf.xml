<config>
    <input>
        <jar in="target/777-FeatherAddons-1.0.jar" out="target/777-FeatherAddons-1.0-obf.jar"/>
    </input>

    <keep-names>
        <class access="public" name="me.darkness.wallet.Main">
            <method access="public" name="onEnable"/>
            <method access="public" name="onDisable"/>
            <method access="public" name="onLoad"/>
        </class>

        <class access="public" name="me.darkness.wallet.commands.PortfelCommand">
            <method access="public" name="onCommand"/>
        </class>
        <class access="public" name="me.darkness.wallet.commands.AdminWalletCommand">
            <method access="public" name="onCommand"/>
            <method access="public" name="onTabComplete"/>
        </class>

        <class access="public" name="me.darkness.wallet.placeholders.WalletPlaceholder">
            <method access="public" name="onRequest"/>
            <method access="public" name="getIdentifier"/>
            <method access="public" name="getAuthor"/>
            <method access="public" name="getVersion"/>
            <method access="public" name="persist"/>
        </class>

        <class access="public" name="me.darkness.wallet.listeners.GuiListener">
            <method access="public" name="onInventoryClick"/>
            <method access="public" name="onInventoryClose"/>
        </class>
    </keep-names>

    <ignore-classes>
        <class template="class org.bukkit.**"/>
        <class template="class org.spigotmc.**"/>
        <class template="class net.md_5.**"/>

        <class template="class me.clip.**"/>

        <class template="class com.mysql.**"/>
        <class template="class org.h2.**"/>
        <class template="class com.mongodb.**"/>
        <class template="class org.bson.**"/>

        <class template="class org.yaml.**"/>

        <class template="class java.**"/>
        <class template="class javax.**"/>
        <class template="class sun.**"/>
        <class template="class com.sun.**"/>

        <class template="class lombok.**"/>

        <class template="class com.google.**"/>
    </ignore-classes>

    <watermark key="777" value="777CODE"/>
    <property name="log-file" value="target/obfuscation.log"/>
    <property name="random-seed" value="maximum"/>
    <property name="string-encryption" value="enable"/>
    <property name="control-flow-obfuscation" value="enable"/>
    <property name="extensive-flow-obfuscation" value="maximum"/>
    <property name="packages-naming" value="123"/>
    <property name="methods-naming" value="iii"/>
    <property name="fields-naming" value="iii"/>
    <property name="local-variables-naming" value="optimize"/>
    <property name="classes-naming" value="iii"/>
    <property name="methods-naming" value="iii"/>
    <property name="local-variables-naming" value="optimize"/>
    <property name="line-numbers" value="obfuscate"/>
    <property name="generics" value="remove"/>
    <property name="inner-classes" value="remove"/>
    <property name="member-reorder" value="enable"/>
</config>
